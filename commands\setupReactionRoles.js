const {
    SlashCommandBuilder,
    PermissionFlagsBits,
    ChannelType,
    MessageFlags,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    ModalBuilder,
    TextInputBuilder,
    TextInputStyle,
    StringSelectMenuBuilder,
    StringSelectMenuOptionBuilder
} = require('discord.js');

const reactionRoleHandler = require('../utils/reactionRoleHandler');
const reactionRoleValidator = require('../utils/reactionRoleValidator');

// In-memory storage for dashboard configurations
const dashboardConfigs = new Map();

// In-memory storage for conversation states (only for Add Role functionality)
const conversationStates = new Map();

// Cleanup old configurations every 30 minutes
setInterval(() => {
    const now = Date.now();
    const maxAge = 30 * 60 * 1000; // 30 minutes

    for (const [configId, config] of dashboardConfigs.entries()) {
        if (now - config.createdAt > maxAge) {
            dashboardConfigs.delete(configId);
            console.log(`[CLEANUP] Removed expired config: ${configId}`);
        }
    }

    for (const [userId, state] of conversationStates.entries()) {
        if (now - state.createdAt > maxAge) {
            conversationStates.delete(userId);
            console.log(`[CLEANUP] Removed expired conversation state for user: ${userId}`);
        }
    }
}, 30 * 60 * 1000);

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-reaction-roles')
        .setDescription('Create a reaction role panel using an interactive dashboard')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('Channel to send the reaction role panel')
                .setRequired(true)
                .addChannelTypes(ChannelType.GuildText)),

    async execute(interaction) {
        const channel = interaction.options.getChannel('channel');

        // Validate bot permissions
        const botMember = interaction.guild.members.me;
        const permissionCheck = reactionRoleValidator.validateBotPermissions(botMember, channel);
        if (!permissionCheck.success) {
            return await interaction.reply({
                content: `❌ **Permission Error**\n${permissionCheck.error}`,
                flags: MessageFlags.Ephemeral
            });
        }

        // Create unique configuration ID
        const configId = `${interaction.user.id}_${Date.now()}`;

        // Initialize configuration
        dashboardConfigs.set(configId, {
            userId: interaction.user.id,
            channelId: channel.id,
            title: null,
            description: null,
            mode: null,
            roles: [],
            color: null,
            imageUrl: null,
            footer: null,
            dashboardMessageId: null,
            dashboardInteraction: interaction, // Store the original interaction for updates
            createdAt: Date.now()
        });

        console.log(`[DEBUG] Created config with ID: ${configId} for user: ${interaction.user.id}`);

        // Show dashboard
        await this.createDashboard(interaction, configId, false);
    },

    // Helper function to check if configuration is complete
    isConfigComplete(config) {
        return config.title && config.description && config.mode && config.roles.length > 0;
    },

    // Helper function to get hex color from input
    parseColorInput(input) {
        // Remove any whitespace
        input = input.trim();

        // If it starts with #, use as-is
        if (input.startsWith('#')) {
            return input;
        }

        // If it's just hex digits, add #
        if (/^[0-9A-Fa-f]{6}$/.test(input)) {
            return `#${input}`;
        }

        // Preset colors
        const presets = {
            'blurple': '#5865F2',
            'discord': '#5865F2',
            'red': '#ED4245',
            'green': '#57F287',
            'yellow': '#FEE75C',
            'orange': '#FF8C42',
            'purple': '#9B59B6',
            'blue': '#3498DB',
            'pink': '#E91E63',
            'black': '#2C2F33',
            'white': '#FFFFFF'
        };

        return presets[input.toLowerCase()] || null;
    },

    // Create or update dashboard display
    async createDashboard(interaction, configId, isUpdate = false) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Use the buildDashboardContent function for consistency
        const { embed, components } = this.buildDashboardContent(config, configId);
        console.log(`[DEBUG] Dashboard created with ${components.length} component rows`);

        if (isUpdate) {
            try {
                await interaction.editReply({
                    embeds: [embed],
                    components: components
                });
            } catch (error) {
                console.error('Error updating dashboard:', error);
                const newMessage = await interaction.followUp({
                    embeds: [embed],
                    components: components,
                    flags: MessageFlags.Ephemeral
                });
                config.dashboardMessageId = newMessage.id;
            }
        } else {
            const message = await interaction.reply({
                embeds: [embed],
                components: components,
                flags: MessageFlags.Ephemeral
            });
            // Store the dashboard message ID for future updates
            config.dashboardMessageId = message.id;
        }
    },



    // Handle dashboard button interactions
    async handleDashboardButton(interaction) {
        const customId = interaction.customId;
        const parts = customId.split('_');
        const action = parts[1];
        const configId = parts.slice(2).join('_');

        console.log(`[DEBUG] Dashboard button: ${action}, configId: ${configId}`);

        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        switch (action) {
            case 'title':
                await this.showTitleModal(interaction, configId);
                break;
            case 'description':
                await this.showDescriptionModal(interaction, configId);
                break;
            case 'mode':
                await this.showModeSelect(interaction, configId);
                break;
            case 'color':
                await this.showColorModal(interaction, configId);
                break;
            case 'image':
                await this.showImageModal(interaction, configId);
                break;
            case 'addrole':
                await this.startRoleConversation(interaction, configId);
                break;
            case 'removerole':
                await this.removeLastRole(interaction, configId);
                break;
            case 'preview':
                await this.showPreview(interaction, configId);
                break;
            case 'footer':
                await this.showFooterModal(interaction, configId);
                break;
            case 'create':
                await this.createPanel(interaction, configId);
                break;
            case 'cancel':
                await this.cancelSetup(interaction, configId);
                break;
                        case 'desc-choice-custom':
            case 'desc-choice-merge':
                if (!config || !config.tempDescription) {
                    return await interaction.reply({
                        content: '❌ Error: Could not find the temporary description. Please try again.',
                        flags: MessageFlags.Ephemeral
                    });
                }

                const choice = interaction.customId.includes('custom') ? 'custom' : 'merge';

                if (choice === 'custom') {
                    config.description = config.tempDescription;
                } else { // merge
                    const defaultDescription = config.roles.map(r => `React to this for the ${r.label} role`).join('\n') || 'React to get a role.';
                    config.description = `${config.tempDescription}\n\n${defaultDescription}`;
                }

                delete config.tempDescription; // Clean up

                await interaction.deferUpdate(); // Acknowledge the button click
                await this.updateDashboardInPlace(interaction, configId, `✅ **Description updated successfully.**`);
                break;
            default:
                await interaction.reply({
                    content: '❌ Unknown action.',
                    flags: MessageFlags.Ephemeral
                });
        }
    },

    // Show title modal
    async showTitleModal(interaction, configId) {
        const modal = new ModalBuilder()
            .setCustomId(`modal_title_${configId}`)
            .setTitle('Set Panel Title');

        const titleInput = new TextInputBuilder()
            .setCustomId('title')
            .setLabel('Panel Title')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter the title for your reaction role panel')
            .setRequired(true)
            .setMaxLength(256);

        const firstActionRow = new ActionRowBuilder().addComponents(titleInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // Show description modal
    async showDescriptionModal(interaction, configId) {
        const modal = new ModalBuilder()
            .setCustomId(`modal_description_${configId}`)
            .setTitle('Set Panel Description');

        const descriptionInput = new TextInputBuilder()
            .setCustomId('description')
            .setLabel('Panel Description (Optional)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Leave blank for a default description, or enter your own.')
            .setRequired(false)
            .setMaxLength(4000);

        const firstActionRow = new ActionRowBuilder().addComponents(descriptionInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // Helper function to clean and format role labels for descriptions
    cleanRoleLabel(label) {
        if (!label || typeof label !== 'string') {
            return 'Unknown Role';
        }

        // Remove surrounding quotes (single, double, or backticks)
        let cleaned = label.replace(/^['"`]+|['"`]+$/g, '');

        // Remove redundant words (like 'level level' or 'team team')
        // Pattern: word followed by same word with optional quotes/spaces
        cleaned = cleaned.replace(/\b(\w+)\b\s*['"`]*\s*\1\b/gi, '$1');

        // Clean up extra spaces and quotes throughout the string
        cleaned = cleaned.replace(/\s*['"`]\s*/g, ' ').trim();

        // Remove any remaining standalone quotes
        cleaned = cleaned.replace(/^['"`]|['"`]$/g, '').trim();

        // Fix capitalization: capitalize first letter if it's all lowercase
        if (cleaned === cleaned.toLowerCase() && cleaned.length > 0) {
            cleaned = cleaned.charAt(0).toUpperCase() + cleaned.slice(1);
        }

        return cleaned || 'Role';
    },

    // Generate a professional default description with clear instructions
    generateDefaultDescription(roles) {
        if (!roles || roles.length === 0) {
            return '**How to get roles:**\nClick the buttons below to assign yourself roles!\n\n*No roles are currently configured.*';
        }

        // Create the header with clear instructions
        let description = '**How to get roles:**\nClick the buttons below to assign yourself roles!\n\n';

        // Add role-specific instructions
        if (roles.length === 1) {
            const cleanLabel = this.cleanRoleLabel(roles[0].label);
            const emoji = roles[0].emoji || '🔹';
            description += `${emoji} Click to get the **${cleanLabel}** role`;
        } else {
            description += '**Available roles:**\n';
            const roleDescriptions = roles.map(role => {
                const cleanLabel = this.cleanRoleLabel(role.label);
                const emoji = role.emoji || '🔹';
                return `${emoji} **${cleanLabel}** - Click to get this role`;
            });
            description += roleDescriptions.join('\n');
        }

        return description;
    },

    // Show color modal
    async showColorModal(interaction, configId) {
        const modal = new ModalBuilder()
            .setCustomId(`modal_color_${configId}`)
            .setTitle('Set Embed Color');

        const colorInput = new TextInputBuilder()
            .setCustomId('color')
            .setLabel('Color (Hex code or preset name)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('e.g., #FF5733, red, blurple, discord')
            .setRequired(false)
            .setMaxLength(20);

        const firstActionRow = new ActionRowBuilder().addComponents(colorInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // Show image modal
    async showImageModal(interaction, configId) {
        const modal = new ModalBuilder()
            .setCustomId(`modal_image_${configId}`)
            .setTitle('Set Embed Image');

        const imageInput = new TextInputBuilder()
            .setCustomId('image')
            .setLabel('Image URL')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('https://example.com/image.png (leave empty to remove)')
            .setRequired(false)
            .setMaxLength(500);

        const firstActionRow = new ActionRowBuilder().addComponents(imageInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // Show footer modal
    async showFooterModal(interaction, configId) {
        const modal = new ModalBuilder()
            .setCustomId(`modal_footer_${configId}`)
            .setTitle('Set Panel Footer');

        const footerInput = new TextInputBuilder()
            .setCustomId('footer')
            .setLabel('Panel Footer (optional, max 2048 chars)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Enter your footer text (leave empty to remove)')
            .setRequired(false)
            .setMaxLength(2048);

        const firstActionRow = new ActionRowBuilder().addComponents(footerInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // Show mode selection
    async showModeSelect(interaction, configId) {
        const select = new StringSelectMenuBuilder()
            .setCustomId(`select_mode_${configId}`)
            .setPlaceholder('Choose reaction role mode')
            .addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel('Single Role')
                    .setDescription('Users can only have one role from this panel at a time')
                    .setValue('single')
                    .setEmoji('1️⃣'),
                new StringSelectMenuOptionBuilder()
                    .setLabel('Multiple Roles')
                    .setDescription('Users can have multiple roles from this panel')
                    .setValue('multiple')
                    .setEmoji('🔢')
            );

        const row = new ActionRowBuilder().addComponents(select);

        await interaction.reply({
            content: '**Select the reaction role mode:**',
            components: [row],
            flags: MessageFlags.Ephemeral
        });
    },

    // Start conversation-based role addition
    async startRoleConversation(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Check if user already has an active conversation
        if (conversationStates.has(interaction.user.id)) {
            return await interaction.reply({
                content: '❌ **You already have an active role conversation in progress.**\n\nPlease complete your current role addition before starting a new one.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Initialize conversation state
        conversationStates.set(interaction.user.id, {
            configId: configId,
            step: 'roles',
            tempRoles: [],
            currentRoleIndex: 0,
            createdAt: Date.now()
        });

        const embed = new EmbedBuilder()
            .setTitle('🎭 Add Roles - Step 1')
            .setDescription('**Please provide your roles in this format:**\n' +
                '`@role @role @role`\n\n' +
                '**Examples:**\n' +
                '• `@Member @VIP @Admin`\n' +
                '• `@Red Team @Blue Team`\n' +
                '• `@Notifications`\n\n' +
                '**Instructions:**\n' +
                '• Mention each role you want to add\n' +
                '• You can add multiple roles in one message\n' +
                '• Maximum 25 roles total per panel\n\n' +
                '**Type your roles now:**')
            .setColor(0x3498db)
            .setFooter({ text: 'You have 5 minutes to respond. Type "cancel" to abort.' });

        await interaction.reply({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
        });

        // Set up message collector
        const filter = (message) => message.author.id === interaction.user.id;
        const collector = interaction.channel.createMessageCollector({
            filter,
            time: 5 * 60 * 1000, // 5 minutes
            max: 1
        });

        collector.on('collect', async (message) => {
            await this.handleRoleInput(interaction, message);
        });

        collector.on('end', async (collected, reason) => {
            if (reason === 'time' && collected.size === 0) {
                conversationStates.delete(interaction.user.id);
                await interaction.followUp({
                    content: '⏰ **Role addition timed out.** Click "Add Role" again to try again.',
                    flags: MessageFlags.Ephemeral
                });
            }
        });
    },

    // Restart role conversation without trying to reply (for error recovery)
    async restartRoleConversation(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return;
        }

        // Clean up any existing conversation state
        conversationStates.delete(interaction.user.id);

        // Initialize new conversation state
        conversationStates.set(interaction.user.id, {
            configId: configId,
            step: 'roles',
            tempRoles: [],
            currentRoleIndex: 0,
            createdAt: Date.now()
        });

        const embed = new EmbedBuilder()
            .setTitle('🎭 Add Roles - Step 1 (Retry)')
            .setDescription('**Please provide your roles in this format:**\n' +
                '`@role @role @role`\n\n' +
                '**Examples:**\n' +
                '• `@Member @VIP @Admin`\n' +
                '• `@Red Team @Blue Team`\n' +
                '• `@Notifications`\n\n' +
                '**Instructions:**\n' +
                '• Mention each role you want to add\n' +
                '• You can add multiple roles in one message\n' +
                '• Maximum 25 roles total per panel\n\n' +
                '**Type your roles now:**')
            .setColor(0x3498db)
            .setFooter({ text: 'You have 5 minutes to respond. Type "cancel" to abort.' });

        await interaction.followUp({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
        });

        // Set up message collector
        const filter = (message) => message.author.id === interaction.user.id;
        const collector = interaction.channel.createMessageCollector({
            filter,
            time: 5 * 60 * 1000, // 5 minutes
            max: 1
        });

        collector.on('collect', async (message) => {
            await this.handleRoleInput(interaction, message);
        });

        collector.on('end', async (collected, reason) => {
            if (reason === 'time' && collected.size === 0) {
                conversationStates.delete(interaction.user.id);
                await interaction.followUp({
                    content: '⏰ **Role addition timed out.** Click "Add Role" again to try again.',
                    flags: MessageFlags.Ephemeral
                });
            }
        });
    },

    // Handle role input parsing for conversation
    async handleRoleInput(interaction, message) {
        const state = conversationStates.get(interaction.user.id);
        if (!state) return;

        // Check for cancel
        if (message.content.toLowerCase() === 'cancel') {
            conversationStates.delete(interaction.user.id);
            await interaction.followUp({
                content: '❌ **Role addition cancelled.**',
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        // Parse role mentions
        const roleParsing = this.parseRoleMentions(message.content, interaction.guild);

        if (!roleParsing.success) {
            await interaction.followUp({
                content: `❌ **Error:** ${roleParsing.error}\n\nPlease try again with the correct format: \`@role @role @role\``,
                flags: MessageFlags.Ephemeral
            });

            // Restart role collection without trying to reply again
            setTimeout(() => this.restartRoleConversation(interaction, state.configId), 2000);
            return;
        }

        // Check total role limit
        const config = dashboardConfigs.get(state.configId);
        if (config.roles.length + roleParsing.roles.length > 25) {
            await interaction.followUp({
                content: `❌ **Too many roles:** You can only have a maximum of 25 roles per panel. You currently have ${config.roles.length} roles and are trying to add ${roleParsing.roles.length} more.`,
                flags: MessageFlags.Ephemeral
            });

            // Restart role collection without trying to reply again
            setTimeout(() => this.restartRoleConversation(interaction, state.configId), 2000);
            return;
        }

        // Validate roles
        const validation = reactionRoleValidator.validateRoleHierarchy(roleParsing.roles, interaction.guild.members.me);
        if (!validation.success) {
            await interaction.followUp({
                content: `❌ **Role Validation Error:** ${validation.error}\n\nPlease try again with valid roles.`,
                flags: MessageFlags.Ephemeral
            });

            // Restart role collection
            setTimeout(() => this.startRoleConversation(interaction, state.configId), 2000);
            return;
        }

        // Store roles and start label collection
        state.tempRoles = roleParsing.roles;
        state.step = 'labels';
        state.currentRoleIndex = 0;

        await this.startLabelCollection(interaction);
    },

    // Parse role mentions from user input (emoji-free)
    parseRoleMentions(content, guild) {
        console.log(`[DEBUG] Parsing input: "${content}"`);

        // Extract role mentions
        const rolePattern = /<@&(\d+)>/g;
        const roleMentions = [...content.matchAll(rolePattern)];

        console.log(`[DEBUG] Found ${roleMentions.length} roles: ${roleMentions.map(m => m[0]).join(', ')}`);

        if (roleMentions.length === 0) {
            return { success: false, error: 'No role mentions found. Please mention roles using @role format.' };
        }

        const roles = [];
        const seenRoles = new Set();

        for (let i = 0; i < roleMentions.length; i++) {
            const roleId = roleMentions[i][1];

            // Skip duplicate roles
            if (seenRoles.has(roleId)) {
                continue;
            }
            seenRoles.add(roleId);

            const role = guild.roles.cache.get(roleId);

            if (!role) {
                return { success: false, error: `Role with ID ${roleId} not found.` };
            }

            roles.push({
                role_id: roleId,
                emoji: null, // No default emoji - emoji-free system
                label: role.name, // Default label, will be customized later
                buttonStyle: null // Will be set during label collection
            });
        }

        return { success: true, roles };
    },

    // Start label collection for each role
    async startLabelCollection(interaction) {
        const state = conversationStates.get(interaction.user.id);
        if (!state || state.currentRoleIndex >= state.tempRoles.length) {
            // All labels collected, add roles to config and return to dashboard
            await this.finishRoleAddition(interaction);
            return;
        }

        const currentRole = state.tempRoles[state.currentRoleIndex];
        const role = interaction.guild.roles.cache.get(currentRole.role_id);

        const embed = new EmbedBuilder()
            .setTitle(`🎭 Add Roles - Step 2a (${state.currentRoleIndex + 1}/${state.tempRoles.length})`)
            .setDescription(`**Setting up role:** ${role} ${currentRole.emoji}\n\n` +
                `**What should the button say?**\n\n` +
                `**Options:**\n` +
                `• Type a custom label (e.g., "VIP Member", "Team Leader")\n` +
                `• Type \`default\` to keep "${currentRole.label}"\n` +
                `• Type \`cancel\` to abort setup\n\n` +
                `**Examples:**\n` +
                `• \`VIP Member\`\n` +
                `• \`Team Leader\`\n` +
                `• \`default\`\n\n` +
                `*After you choose the label, you'll pick the button color with easy-to-use buttons.*`)
            .setColor(0x3498db)
            .setFooter({ text: 'Just type the label text • Next: Choose button color • 5 minutes to respond' });

        await interaction.followUp({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
        });

        // Set up message collector for label
        const filter = (message) => message.author.id === interaction.user.id;
        const collector = interaction.channel.createMessageCollector({
            filter,
            time: 5 * 60 * 1000, // 5 minutes
            max: 1
        });

        collector.on('collect', async (message) => {
            await this.handleLabelInput(interaction, message);
        });

        collector.on('end', async (collected, reason) => {
            if (reason === 'time' && collected.size === 0) {
                conversationStates.delete(interaction.user.id);
                await interaction.followUp({
                    content: '⏰ **Role addition timed out.** Click "Add Role" again to try again.',
                    flags: MessageFlags.Ephemeral
                });
            }
        });
    },

    // Handle label input (step 1 of 2-step process)
    async handleLabelInput(interaction, message) {
        const state = conversationStates.get(interaction.user.id);
        if (!state) return;

        // Check for cancel
        if (message.content.toLowerCase() === 'cancel') {
            conversationStates.delete(interaction.user.id);
            await interaction.followUp({
                content: '❌ **Role addition cancelled.**',
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        // Handle simple label input (step 1 of 2)
        const input = message.content.trim();
        const inputLower = input.toLowerCase();

        let label = input;

        if (inputLower === 'default') {
            // Keep the default label (already set)
            label = state.tempRoles[state.currentRoleIndex].label;
        } else {
            // Custom label - validate it
            if (label.length > 80) {
                await interaction.followUp({
                    content: `❌ **Label too long!** Discord button labels must be 80 characters or less. Your label is ${label.length} characters.`,
                    flags: MessageFlags.Ephemeral
                });
                await this.startLabelCollection(interaction);
                return;
            }

            if (label.length === 0) {
                await interaction.followUp({
                    content: `❌ **Label cannot be empty!** Please provide a valid label.`,
                    flags: MessageFlags.Ephemeral
                });
                await this.startLabelCollection(interaction);
                return;
            }
        }

        // Store the label and move to button style selection
        state.tempRoles[state.currentRoleIndex].label = label;

        // Now show button style selection (step 2)
        await this.showButtonStyleSelection(interaction, state.configId, state.currentRoleIndex);
    },

    // Show button style selection with visual buttons (step 2 of 2)
    async showButtonStyleSelection(interaction, configId, roleIndex) {
        const state = conversationStates.get(interaction.user.id);
        if (!state) return;

        const config = dashboardConfigs.get(configId);
        if (!config) return;

        const currentRole = state.tempRoles[roleIndex];
        const role = interaction.guild.roles.cache.get(currentRole.role_id);

        const embed = new EmbedBuilder()
            .setTitle(`🎭 Add Roles - Step 2b (${roleIndex + 1}/${state.tempRoles.length})`)
            .setDescription(`**Choosing button color for role:** ${role}\n\n` +
                `**Current emoji:** ${currentRole.emoji}\n` +
                `**Label:** ${currentRole.label}\n\n` +
                `**Choose a button color by clicking one of the buttons below:**`)
            .setColor(0x9b59b6)
            .setFooter({ text: 'Click a button to select the color for this role.' });

        const { ButtonStyle, ButtonBuilder, ActionRowBuilder } = require('discord.js');

        const styleButtons = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`style_primary_${configId}_${roleIndex}`)
                    .setLabel('Primary (Blue)')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔵'),
                new ButtonBuilder()
                    .setCustomId(`style_secondary_${configId}_${roleIndex}`)
                    .setLabel('Secondary (Gray)')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⚪'),
                new ButtonBuilder()
                    .setCustomId(`style_success_${configId}_${roleIndex}`)
                    .setLabel('Success (Green)')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('🟢'),
                new ButtonBuilder()
                    .setCustomId(`style_danger_${configId}_${roleIndex}`)
                    .setLabel('Danger (Red)')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🔴'),
                new ButtonBuilder()
                    .setCustomId(`style_skip_${configId}_${roleIndex}`)
                    .setLabel('Skip')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⏭️')
            );

        await interaction.followUp({
            embeds: [embed],
            components: [styleButtons],
            flags: MessageFlags.Ephemeral
        });
    },

    // Handle button style selection
    async handleButtonStyleSelection(interaction) {
        const customId = interaction.customId;
        const parts = customId.split('_');
        const style = parts[1];
        const roleIndex = parseInt(parts[parts.length - 1]);
        const configId = parts.slice(2, -1).join('_');

        const state = conversationStates.get(interaction.user.id);
        if (!state) {
            return await interaction.reply({ content: '❌ Session expired. Please start over.', flags: MessageFlags.Ephemeral });
        }

        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({ content: '❌ Configuration not found. Please start over.', flags: MessageFlags.Ephemeral });
        }

        const { ButtonStyle } = require('discord.js');
        const styleMap = {
            'primary': ButtonStyle.Primary,
            'secondary': ButtonStyle.Secondary,
            'success': ButtonStyle.Success,
            'danger': ButtonStyle.Danger,
            'skip': ButtonStyle.Secondary
        };
        state.tempRoles[roleIndex].buttonStyle = styleMap[style];

        state.currentRoleIndex++;

        if (state.currentRoleIndex < state.tempRoles.length) {
            let styleName, styleEmoji;
            if (style === 'skip') {
                styleName = 'Default Gray';
                styleEmoji = '⏭️';
            } else {
                styleName = style.charAt(0).toUpperCase() + style.slice(1);
                styleEmoji = { primary: '🔵', secondary: '⚪', success: '🟢', 'danger': ButtonStyle.Danger }[style];
            }

            await interaction.update({
                content: `✅ **${styleName} selected!** ${styleEmoji}\n\n**Label:** "${state.tempRoles[roleIndex].label}"\n**Color:** ${styleName}`,
                embeds: [],
                components: []
            });

            await this.startLabelCollection(interaction);
        } else {
            // Last role. Acknowledge the button click without a new message.
            await interaction.deferUpdate();
            // Proceed to update the dashboard.
            await this.finishRoleAddition(interaction);
        }
    },

    // Finish role addition
    async finishRoleAddition(interaction) {
        const state = conversationStates.get(interaction.user.id);
        if (!state) return;

        const config = dashboardConfigs.get(state.configId);
        if (!config) {
            conversationStates.delete(interaction.user.id);
            return await interaction.followUp({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        config.roles.push(...state.tempRoles);
        conversationStates.delete(interaction.user.id);

        // Auto-update description if it's currently the "no roles" default
        console.log(`[DEBUG] Checking description auto-update. Current description: "${config.description}"`);
        console.log(`[DEBUG] Number of roles after addition: ${config.roles.length}`);

        if (!config.description ||
            config.description.includes('*No roles are currently configured.*') ||
            config.description.includes('*No roles are currently available.*') ||
            config.description.includes('Role Assignment Panel') ||
            config.description.includes('Premium Role Assignment Center') ||
            config.description.includes('**How to get roles:**')) {
            console.log(`[DEBUG] Triggering description auto-update...`);
            config.description = this.generateDefaultDescription(config.roles);
            console.log(`[DEBUG] Advanced description generated with ${config.roles.length} roles`);
        } else {
            console.log(`[DEBUG] Description auto-update not triggered - description doesn't match criteria`);
        }

        try {
            // Update the main dashboard panel
            const { embed, components } = this.buildDashboardContent(config, state.configId);
            await config.dashboardInteraction.editReply({
                embeds: [embed],
                components: components
            });
            console.log(`[DEBUG] Dashboard updated successfully after role addition for config: ${state.configId}`);

            // Send a separate success notification as requested
            await interaction.followUp({
                content: '✅ Roles added! Return to the dashboard for further configuration.',
                flags: MessageFlags.Ephemeral
            });

        } catch (error) {
            console.error(`Error updating dashboard after role addition for config ${state.configId}:`, error);
            await interaction.followUp({
                content: `✅ **Successfully added ${state.tempRoles.length} role(s)!**
⚠️ The dashboard failed to update automatically. It will refresh on your next action.`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    

    // Handle modal submissions
    async handleDashboardModal(interaction) {
        const customId = interaction.customId;
        const parts = customId.split('_');
        const action = parts[1];
        const configId = parts.slice(2).join('_');

        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        switch (action) {
            case 'title':
                const title = interaction.fields.getTextInputValue('title');
                const titleValidation = reactionRoleValidator.validateTitle(title);
                if (!titleValidation.success) {
                    return await interaction.reply({
                        content: `❌ **Invalid title:** ${titleValidation.error}`,
                        flags: MessageFlags.Ephemeral
                    });
                }
                config.title = title;

                // Defer the modal reply to avoid creating new messages
                await interaction.deferReply({ flags: MessageFlags.Ephemeral });

                // Update the original dashboard message in place
                try {
                    const { embed, components } = this.buildDashboardContent(config, configId);
                    await config.dashboardInteraction.editReply({
                        embeds: [embed],
                        components: components
                    });

                    // Send success message
                    await interaction.followUp({
                        content: `✅ **Title set:** ${title}`,
                        flags: MessageFlags.Ephemeral
                    });
                    console.log(`[DEBUG] Successfully updated dashboard in place after title set`);
                } catch (error) {
                    console.error('Error updating dashboard after title set:', error);
                    await interaction.followUp({
                        content: `✅ **Title set:** ${title}\n⚠️ Dashboard update failed. Please refresh by clicking any button.`,
                        flags: MessageFlags.Ephemeral
                    });
                }
                break;

            case 'description':
                const customDescription = interaction.fields.getTextInputValue('description');
                const descValidation = reactionRoleValidator.validateDescription(customDescription);

                if (!descValidation.success) {
                    return await interaction.reply({
                        content: `❌ **Invalid description:** ${descValidation.error}`,
                        flags: MessageFlags.Ephemeral
                    });
                }

                if (!customDescription.trim()) {
                    // If description is empty, generate and set the default
                    const defaultDescription = this.generateDefaultDescription(config.roles);
                    config.description = defaultDescription;

                    await interaction.deferReply({ flags: MessageFlags.Ephemeral });
                    await this.updateDashboardInPlace(interaction, configId, '✅ **Description set to default.**');
                } else {
                    // If a custom description is provided, ask for confirmation
                    config.tempDescription = customDescription;
                    const embed = new EmbedBuilder()
                        .setTitle('📝 Custom Description')
                        .setDescription('How should this description be used?')
                        .setColor(0x3498db);

                    const buttons = new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setCustomId(`dashboard_desc-choice-custom_${configId}`)
                                .setLabel('Use Custom Only')
                                .setStyle(ButtonStyle.Primary),
                            new ButtonBuilder()
                                .setCustomId(`dashboard_desc-choice-merge_${configId}`)
                                .setLabel('Merge with Default')
                                .setStyle(ButtonStyle.Secondary)
                        );

                    await interaction.reply({
                        embeds: [embed],
                        components: [buttons],
                        flags: MessageFlags.Ephemeral
                    });
                }
                break;

            case 'color':
                const colorInput = interaction.fields.getTextInputValue('color');
                let colorMessage;

                if (colorInput.trim() === '') {
                    // Remove color (use default)
                    config.color = null;
                    colorMessage = '✅ **Color reset to default:** #5865F2';
                } else {
                    const parsedColor = this.parseColorInput(colorInput);
                    if (!parsedColor) {
                        return await interaction.reply({
                            content: `❌ **Invalid color:** "${colorInput}"\n\nValid formats:\n• Hex codes: #FF5733 or FF5733\n• Preset names: red, green, blue, blurple, discord, etc.`,
                            flags: MessageFlags.Ephemeral
                        });
                    }
                    config.color = parsedColor;
                    colorMessage = `✅ **Color set:** ${parsedColor}`;
                }

                // Defer the modal reply to avoid creating new messages
                await interaction.deferReply({ flags: MessageFlags.Ephemeral });

                // Update the original dashboard message in place
                try {
                    const { embed, components } = this.buildDashboardContent(config, configId);
                    await config.dashboardInteraction.editReply({
                        embeds: [embed],
                        components: components
                    });

                    // Send success message
                    await interaction.followUp({
                        content: colorMessage,
                        flags: MessageFlags.Ephemeral
                    });
                    console.log(`[DEBUG] Successfully updated dashboard in place after color set`);
                } catch (error) {
                    console.error('Error updating dashboard after color set:', error);
                    await interaction.followUp({
                        content: `${colorMessage}\n⚠️ Dashboard update failed. Please refresh by clicking any button.`,
                        flags: MessageFlags.Ephemeral
                    });
                }
                break;

            case 'image':
                const imageInput = interaction.fields.getTextInputValue('image');
                let imageMessage;

                if (imageInput.trim() === '') {
                    // Remove image
                    config.imageUrl = null;
                    imageMessage = '✅ **Image removed**';
                } else {
                    const imageValidation = reactionRoleValidator.validateImageUrl(imageInput);
                    if (!imageValidation.success) {
                        return await interaction.reply({
                            content: `❌ **Invalid image URL:** ${imageValidation.error}`,
                            flags: MessageFlags.Ephemeral
                        });
                    }
                    config.imageUrl = imageInput;
                    imageMessage = `✅ **Image set:** ${imageInput}`;
                }

                // Defer the modal reply to avoid creating new messages
                await interaction.deferReply({ flags: MessageFlags.Ephemeral });

                // Update the original dashboard message in place
                try {
                    const { embed, components } = this.buildDashboardContent(config, configId);
                    await config.dashboardInteraction.editReply({
                        embeds: [embed],
                        components: components
                    });

                    // Send success message
                    await interaction.followUp({
                        content: imageMessage,
                        flags: MessageFlags.Ephemeral
                    });
                    console.log(`[DEBUG] Successfully updated dashboard in place after image set`);
                } catch (error) {
                    console.error('Error updating dashboard after image set:', error);
                    await interaction.followUp({
                        content: `${imageMessage}\n⚠️ Dashboard update failed. Please refresh by clicking any button.`,
                        flags: MessageFlags.Ephemeral
                    });
                }
                break;

            case 'footer':
                const footerInput = interaction.fields.getTextInputValue('footer');
                let footerMessage;

                if (footerInput.trim() === '') {
                    // Remove footer
                    config.footer = null;
                    footerMessage = '✅ **Footer removed**';
                } else {
                    config.footer = footerInput.trim();
                    footerMessage = `✅ **Footer set:** ${config.footer.length > 100 ? config.footer.substring(0, 100) + '...' : config.footer}`;
                }

                // Defer the modal reply to avoid creating new messages
                await interaction.deferReply({ flags: MessageFlags.Ephemeral });

                // Update the original dashboard message in place
                try {
                    const { embed, components } = this.buildDashboardContent(config, configId);
                    await config.dashboardInteraction.editReply({
                        embeds: [embed],
                        components: components
                    });

                    // Send success message
                    await interaction.followUp({
                        content: footerMessage,
                        flags: MessageFlags.Ephemeral
                    });
                    console.log(`[DEBUG] Successfully updated dashboard in place after footer set`);
                } catch (error) {
                    console.error('Error updating dashboard after footer set:', error);
                    await interaction.followUp({
                        content: `${footerMessage}\n⚠️ Dashboard update failed. Please refresh by clicking any button.`,
                        flags: MessageFlags.Ephemeral
                    });
                }
                break;

            default:
                await interaction.reply({
                    content: '❌ Unknown modal action.',
                    flags: MessageFlags.Ephemeral
                });
                return;
        }
    },

    // Handle select menu interactions
    async handleDashboardSelect(interaction) {
        const customId = interaction.customId;
        const parts = customId.split('_');
        const action = parts[1];
        const configId = parts.slice(2).join('_');

        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        if (action === 'mode') {
            const mode = interaction.values[0];
            config.mode = mode;

            // Defer the update to avoid creating new messages
            await interaction.deferUpdate();

            // Update dashboard immediately
            try {
                await this.updateDashboardInPlace(interaction, configId);
            } catch (error) {
                console.error('Error updating dashboard after mode selection:', error);
                await interaction.followUp({
                    content: '⚠️ **Mode saved successfully, but dashboard update failed.** Please run the command again to see the updated configuration.',
                    flags: MessageFlags.Ephemeral
                });
            }
        } else if (action === 'buttonstyle') {
            const styleValue = interaction.values[0];

            // Map style values to ButtonStyle constants
            const styleMap = {
                'primary': ButtonStyle.Primary,
                'secondary': ButtonStyle.Secondary,
                'success': ButtonStyle.Success,
                'danger': ButtonStyle.Danger
            };

            config.buttonStyle = styleMap[styleValue];

            // Defer the update to avoid creating new messages
            await interaction.deferUpdate();

            // Update dashboard immediately
            try {
                await this.createDashboard(interaction, configId, true);
            } catch (error) {
                console.error('Error updating dashboard after button style selection:', error);
                await interaction.followUp({
                    content: '⚠️ **Button style saved successfully, but dashboard update failed.** Please run the command again to see the updated configuration.',
                    flags: MessageFlags.Ephemeral
                });
            }
        }
    },

    // Remove last role
    async removeLastRole(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        if (config.roles.length === 0) {
            return await interaction.reply({
                content: '❌ No roles to remove.',
                flags: MessageFlags.Ephemeral
            });
        }

        const removedRole = config.roles.pop();
        const role = interaction.guild.roles.cache.get(removedRole.role_id);

        await interaction.reply({
            content: `✅ **Removed role:** ${removedRole.emoji} ${removedRole.label} (${role})`,
            flags: MessageFlags.Ephemeral
        });

        // Update dashboard
        setTimeout(() => this.createDashboard(interaction, configId, true), 1000);
    },

    // Show preview
    async showPreview(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Validate configuration completeness
        if (!this.isConfigComplete(config)) {
            return await interaction.reply({
                content: '❌ **Cannot preview incomplete configuration.**\n\nPlease ensure you have set:\n• Title\n• Description\n• Mode\n• At least one role',
                flags: MessageFlags.Ephemeral
            });
        }

        const channel = interaction.guild.channels.cache.get(config.channelId);

        // Validate roles before preview
        const roleValidation = await reactionRoleValidator.validateRoleHierarchy(config.roles, interaction.guild.members.me);
        if (!roleValidation.success) {
            return await interaction.reply({
                content: `❌ **Role Validation Error:** ${roleValidation.error}`,
                flags: MessageFlags.Ephemeral
            });
        }

        try {
            // Create preview embed and components manually
            const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

            const embed = new EmbedBuilder()
                .setTitle(config.title)
                .setDescription(config.description)
                .setColor(config.color ? parseInt(config.color.replace('#', ''), 16) : 0x5865F2)
                .setFooter({
                    text: `Mode: ${config.mode === 'single' ? 'Single Role (selecting one removes others)' : 'Multiple Roles (toggle on/off)'}`,
                    iconURL: interaction.guild.iconURL()
                });

            // Add image if set
            if (config.imageUrl) {
                embed.setImage(config.imageUrl);
            }

            // Create preview buttons (disabled for preview)
            const components = [];
            const roles = config.roles;

            for (let i = 0; i < roles.length; i += 5) {
                const row = new ActionRowBuilder();
                const rowRoles = roles.slice(i, i + 5);

                for (const role of rowRoles) {
                    const guildRole = interaction.guild.roles.cache.get(role.role_id);
                    row.addComponents(
                        new ButtonBuilder()
                            .setCustomId(`preview_${role.role_id}`)
                            .setLabel(`${role.emoji} ${role.label}`)
                            .setStyle(role.buttonStyle || ButtonStyle.Secondary)
                            .setDisabled(true) // Disabled for preview
                    );
                }

                components.push(row);
            }

            await interaction.reply({
                content: `**🔍 Preview for ${channel}:**\n*This is how your reaction role panel will look. Buttons are disabled in preview mode.*`,
                embeds: [embed],
                components: components,
                flags: MessageFlags.Ephemeral
            });

        } catch (error) {
            console.error('Preview error:', error);
            await interaction.reply({
                content: `❌ **Preview Error:** ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    // Create final panel
    async createPanel(interaction, configId) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            return await interaction.reply({
                content: '❌ Configuration not found. Please start over.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Validate configuration completeness
        if (!this.isConfigComplete(config)) {
            return await interaction.reply({
                content: '❌ **Cannot create incomplete panel.**\n\nPlease ensure you have set:\n• Title\n• Description\n• Mode\n• At least one role',
                flags: MessageFlags.Ephemeral
            });
        }

        const channel = interaction.guild.channels.cache.get(config.channelId);

        // Validate bot permissions in target channel
        const botMember = interaction.guild.members.me;
        const permissionCheck = reactionRoleValidator.validateBotPermissions(botMember, channel);
        if (!permissionCheck.success) {
            return await interaction.reply({
                content: `❌ **Permission Error in ${channel}:**\n${permissionCheck.error}`,
                flags: MessageFlags.Ephemeral
            });
        }

        // Validate role hierarchy
        const roleValidation = await reactionRoleValidator.validateRoleHierarchy(config.roles, botMember);
        if (!roleValidation.success) {
            return await interaction.reply({
                content: `❌ **Role Validation Error:** ${roleValidation.error}`,
                flags: MessageFlags.Ephemeral
            });
        }

        // Create panel data
        const panelData = {
            title: config.title,
            description: config.description,
            mode: config.mode,
            roles: config.roles,
            color: config.color,
            imageUrl: config.imageUrl,
            footer: config.footer
        };

        try {
            // Show creating message
            await interaction.reply({
                content: `🔄 **Creating reaction role panel in ${channel}...**`,
                flags: MessageFlags.Ephemeral
            });

            // Create the panel
            const result = await reactionRoleHandler.createReactionRolePanel(channel, panelData, interaction.guild);

            if (result.success) {
                // Clean up configuration
                dashboardConfigs.delete(configId);

                // Update with success message
                await interaction.editReply({
                    content: `✅ **Reaction role panel created successfully in ${channel}!**\n\n` +
                        `**Panel Details:**\n` +
                        `• **Title:** ${config.title}\n` +
                        `• **Mode:** ${config.mode === 'single' ? 'Single Role' : 'Multiple Roles'}\n` +
                        `• **Roles:** ${config.roles.length} role(s) configured\n` +
                        `• **Message ID:** ${result.messageId}\n\n` +
                        `Users can now click the buttons to get or remove roles!`
                });
            } else {
                await interaction.editReply({
                    content: `❌ **Panel Creation Failed:** ${result.error}`
                });
            }

        } catch (error) {
            console.error('Panel creation error:', error);

            try {
                await interaction.editReply({
                    content: `❌ **Creation Error:** ${error.message}`
                });
            } catch (editError) {
                console.error('Error editing reply:', editError);
                await interaction.followUp({
                    content: `❌ **Creation Error:** ${error.message}`,
                    flags: MessageFlags.Ephemeral
                });
            }
        }
    },

    // Cancel setup
    async cancelSetup(interaction, configId) {
        dashboardConfigs.delete(configId);

        await interaction.reply({
            content: '❌ **Setup cancelled.** Configuration has been discarded.',
            flags: MessageFlags.Ephemeral
        });
    },

    // Update dashboard in place (for immediate updates)
    async updateDashboardInPlace(interaction, configId, successMessage = null) {
        const config = dashboardConfigs.get(configId);
        if (!config) {
            console.error('Config not found for updateDashboardInPlace:', configId);
            return;
        }

        try {
            const { embed, components } = this.buildDashboardContent(config, configId);
            await config.dashboardInteraction.editReply({
                embeds: [embed],
                components: components
            });

            if (successMessage) {
                await interaction.followUp({ content: successMessage, flags: MessageFlags.Ephemeral });
            }
            console.log(`[DEBUG] Successfully updated dashboard in place for config ${configId}`);
        } catch (error) {
            console.error('Error in updateDashboardInPlace:', error);
            await interaction.followUp({ 
                content: '⚠️ Dashboard update failed. It will refresh on your next action.', 
                flags: MessageFlags.Ephemeral 
            });
        }
    },

    // Build dashboard content (extracted from createDashboard for reuse)
    buildDashboardContent(config, configId) {
        const isComplete = this.isConfigComplete(config);

        const embed = new EmbedBuilder()
            .setTitle('🎭 Reaction Role Setup Dashboard')
            .setDescription('Configure your reaction role panel using the buttons below.')
            .setColor(config.color ? parseInt(config.color.replace('#', ''), 16) : 0x5865F2)
            .addFields(
                {
                    name: '📝 Title',
                    value: config.title || '*Not set*',
                    inline: true
                },
                {
                    name: '📄 Description',
                    value: config.description ? (config.description.length > 50 ? config.description.substring(0, 50) + '...' : config.description) : '*Not set*',
                    inline: true
                },
                {
                    name: '⚙️ Mode',
                    value: config.mode ? (config.mode === 'single' ? '🔘 Single Role' : '✅ Multiple Roles') : '*Not set*',
                    inline: true
                },
                {
                    name: '🎨 Color',
                    value: config.color ? `${config.color}` : '*Default (#5865F2)*',
                    inline: true
                },
                {
                    name: '🖼️ Image',
                    value: config.imageUrl ? '✅ Set' : '*Not set*',
                    inline: true
                },
                {
                    name: '🔖 Footer',
                    value: config.footer ? (config.footer.length > 50 ? config.footer.substring(0, 50) + '...' : config.footer) : '*Not set*',
                    inline: true
                },
                {
                    name: '📊 Channel',
                    value: `<#${config.channelId}>`,
                    inline: true
                }
            );

        if (config.roles.length > 0) {
            // Get style name from ButtonStyle enum
            const { ButtonStyle } = require('discord.js');
            const styleLookup = Object.entries(ButtonStyle).reduce((obj, [key, value]) => {
                obj[value] = key.toLowerCase();
                return obj;
            }, {});
            
            const roleList = config.roles.map((role, index) => {
                const styleName = styleLookup[role.buttonStyle] || 'secondary';
                return `${index + 1}. ${role.emoji} **${role.label}** (<@&${role.role_id}>) - ${styleName}`;
            }).join('\n');
            
            embed.addFields({
                name: '🎭 Roles',
                value: roleList.length > 1024 ? roleList.substring(0, 1021) + '...' : roleList,
                inline: false
            });
        } else {
            embed.addFields({
                name: '🎭 Roles',
                value: '*No roles added*',
                inline: false
            });
        }

        if (isComplete) {
            embed.setFooter({ text: '✅ Configuration complete! You can now create your panel.' });
        } else {
            embed.setFooter({ text: '⚠️ Please complete all required fields before creating the panel.' });
        }

        // Create action rows
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`dashboard_title_${configId}`)
                    .setLabel('Set Title')
                    .setStyle(config.title ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('📝'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_description_${configId}`)
                    .setLabel('Set Description')
                    .setStyle(config.description ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('📄'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_mode_${configId}`)
                    .setLabel('Set Mode')
                    .setStyle(config.mode ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('⚙️'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_color_${configId}`)
                    .setLabel('Set Color')
                    .setStyle(config.color ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('🎨'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_image_${configId}`)
                    .setLabel('Set Image')
                    .setStyle(config.imageUrl ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('🖼️')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`dashboard_addrole_${configId}`)
                    .setLabel('Add Role')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId(`dashboard_removerole_${configId}`)
                    .setLabel('Remove Last Role')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('➖')
                    .setDisabled(config.roles.length === 0),
                new ButtonBuilder()
                    .setCustomId(`dashboard_preview_${configId}`)
                    .setLabel('Preview')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔍')
                    .setDisabled(!isComplete),
                new ButtonBuilder()
                    .setCustomId(`dashboard_footer_${configId}`)
                    .setLabel('Set Footer')
                    .setStyle(config.footer ? ButtonStyle.Success : ButtonStyle.Secondary)
                    .setEmoji('🔖')
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`dashboard_create_${configId}`)
                    .setLabel('Create Panel')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✅')
                    .setDisabled(!isComplete),
                new ButtonBuilder()
                    .setCustomId(`dashboard_cancel_${configId}`)
                    .setLabel('Cancel')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
            );

        const components = [row1, row2, row3];

        return { embed, components };
    }
};