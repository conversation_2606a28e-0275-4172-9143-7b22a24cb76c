const { PermissionFlagsBits } = require('discord.js');

class ReactionRoleValidator {
    /**
     * Validate bot permissions for reaction roles
     * @param {GuildMember} botMember - Bot's guild member object
     * @param {TextChannel} channel - Target channel (optional)
     * @returns {Object} Validation result
     */
    validateBotPermissions(botMember, channel = null) {
        const requiredPermissions = [
            { flag: PermissionFlagsBits.ManageRoles, name: 'Manage Roles' },
            { flag: PermissionFlagsBits.SendMessages, name: 'Send Messages' },
            { flag: PermissionFlagsBits.EmbedLinks, name: 'Embed Links' },
            { flag: PermissionFlagsBits.UseExternalEmojis, name: 'Use External Emojis' }
        ];

        // Check guild-level permissions
        const missingGuildPerms = [];
        for (const perm of requiredPermissions) {
            if (!botMember.permissions.has(perm.flag)) {
                missingGuildPerms.push(perm.name);
            }
        }

        if (missingGuildPerms.length > 0) {
            return {
                success: false,
                error: `Missing guild permissions: ${missingGuildPerms.join(', ')}`
            };
        }

        // Check channel-specific permissions if channel provided
        if (channel) {
            const channelPerms = channel.permissionsFor(botMember);
            const missingChannelPerms = [];

            const channelRequiredPerms = [
                { flag: PermissionFlagsBits.SendMessages, name: 'Send Messages' },
                { flag: PermissionFlagsBits.EmbedLinks, name: 'Embed Links' },
                { flag: PermissionFlagsBits.UseExternalEmojis, name: 'Use External Emojis' }
            ];

            for (const perm of channelRequiredPerms) {
                if (!channelPerms.has(perm.flag)) {
                    missingChannelPerms.push(perm.name);
                }
            }

            if (missingChannelPerms.length > 0) {
                return {
                    success: false,
                    error: `Missing channel permissions in #${channel.name}: ${missingChannelPerms.join(', ')}`
                };
            }
        }

        return { success: true };
    }

    /**
     * Validate role hierarchy and permissions
     * @param {Array} roleConfigs - Array of role configurations
     * @param {GuildMember} botMember - Bot's guild member object
     * @returns {Object} Validation result
     */
    validateRoleHierarchy(roleConfigs, botMember) {
        const botHighestRole = botMember.roles.highest;
        const issues = [];

        for (const roleConfig of roleConfigs) {
            const role = botMember.guild.roles.cache.get(roleConfig.role_id);
            
            if (!role) {
                issues.push(`Role with ID "${roleConfig.role_id}" not found`);
                continue;
            }

            // Check hierarchy
            if (role.position >= botHighestRole.position) {
                issues.push(`Role "${role.name}" is higher than or equal to bot's highest role`);
            }

            // Check if role is managed by integration
            if (role.managed) {
                issues.push(`Role "${role.name}" is managed by an integration and cannot be assigned manually`);
            }

            // Check if role is @everyone
            if (role.id === botMember.guild.id) {
                issues.push(`Cannot assign the @everyone role`);
            }
        }

        if (issues.length > 0) {
            return {
                success: false,
                error: `Role hierarchy issues: ${issues.join(', ')}`
            };
        }

        return { success: true };
    }

    /**
     * Validate emoji format (Unicode or Discord custom emoji)
     * @param {string} emoji - Emoji string to validate
     * @returns {Object} Validation result
     */
    validateEmoji(emoji) {
        if (!emoji || typeof emoji !== 'string') {
            return {
                success: false,
                error: 'Emoji cannot be empty'
            };
        }

        // Unicode emoji patterns (comprehensive)
        const unicodeEmojiPatterns = [
            /[\u{1F600}-\u{1F64F}]/u, // Emoticons
            /[\u{1F300}-\u{1F5FF}]/u, // Misc Symbols and Pictographs
            /[\u{1F680}-\u{1F6FF}]/u, // Transport and Map
            /[\u{1F1E0}-\u{1F1FF}]/u, // Regional indicators
            /[\u{2600}-\u{26FF}]/u,   // Misc symbols
            /[\u{2700}-\u{27BF}]/u,   // Dingbats
            /[\u{1F900}-\u{1F9FF}]/u, // Supplemental Symbols and Pictographs
            /[\u{1FA70}-\u{1FAFF}]/u, // Symbols and Pictographs Extended-A
            /[\u{FE00}-\u{FE0F}]/u,   // Variation Selectors
            /[\u{200D}]/u,            // Zero Width Joiner
            /[\u{20E3}]/u             // Combining Enclosing Keycap
        ];

        // Discord custom emoji pattern
        const discordEmojiRegex = /^<a?:\w+:\d+>$/;

        // Check if it's a Discord custom emoji
        if (discordEmojiRegex.test(emoji)) {
            return { success: true, type: 'custom' };
        }

        // Check if it's a Unicode emoji
        for (const pattern of unicodeEmojiPatterns) {
            if (pattern.test(emoji)) {
                return { success: true, type: 'unicode' };
            }
        }

        // Additional check for common Unicode emojis that might not match above patterns
        try {
            // This is a more lenient check for Unicode emojis
            const codePoints = [...emoji].map(char => char.codePointAt(0));
            const hasEmojiCodePoint = codePoints.some(cp => 
                (cp >= 0x1F600 && cp <= 0x1F64F) || // Emoticons
                (cp >= 0x1F300 && cp <= 0x1F5FF) || // Misc Symbols
                (cp >= 0x1F680 && cp <= 0x1F6FF) || // Transport
                (cp >= 0x1F1E0 && cp <= 0x1F1FF) || // Regional
                (cp >= 0x2600 && cp <= 0x26FF) ||   // Misc
                (cp >= 0x2700 && cp <= 0x27BF) ||   // Dingbats
                (cp >= 0x1F900 && cp <= 0x1F9FF) || // Supplemental
                (cp >= 0x1FA70 && cp <= 0x1FAFF)    // Extended
            );

            if (hasEmojiCodePoint) {
                return { success: true, type: 'unicode' };
            }
        } catch (error) {
            // Ignore error and continue to failure
        }

        return {
            success: false,
            error: `Invalid emoji format: "${emoji}". Use Unicode emoji (🎮) or Discord emoji format (<:name:id>)`
        };
    }

    /**
     * Validate role configuration string
     * @param {string} rolesString - The roles string to validate
     * @param {Guild} guild - The Discord guild
     * @returns {Object} Validation result
     */
    validateRolesString(rolesString, guild) {
        if (!rolesString || typeof rolesString !== 'string') {
            return {
                success: false,
                error: 'Roles string cannot be empty'
            };
        }

        const rolePairs = rolesString.split(',').map(pair => pair.trim()).filter(pair => pair.length > 0);
        
        if (rolePairs.length === 0) {
            return {
                success: false,
                error: 'At least one role must be specified'
            };
        }

        if (rolePairs.length > 25) {
            return {
                success: false,
                error: 'Maximum of 25 roles allowed per panel (Discord button limit)'
            };
        }

        const roleIds = new Set();
        const labels = new Set();

        for (let i = 0; i < rolePairs.length; i++) {
            const pair = rolePairs[i];
            const parts = pair.split(':');
            
            if (parts.length !== 3) {
                return {
                    success: false,
                    error: `Invalid format for role pair ${i + 1}: "${pair}". Expected format: role_id:emoji:label`
                };
            }

            const [roleId, emoji, label] = parts.map(part => part.trim());

            // Validate role ID
            if (!roleId || !/^\d+$/.test(roleId)) {
                return {
                    success: false,
                    error: `Invalid role ID in pair ${i + 1}: "${roleId}". Must be a valid Discord role ID`
                };
            }

            // Check for duplicate role IDs
            if (roleIds.has(roleId)) {
                return {
                    success: false,
                    error: `Duplicate role ID: "${roleId}"`
                };
            }
            roleIds.add(roleId);

            // Validate role exists
            const role = guild.roles.cache.get(roleId);
            if (!role) {
                return {
                    success: false,
                    error: `Role with ID "${roleId}" not found in this server`
                };
            }

            // Validate emoji
            const emojiValidation = this.validateEmoji(emoji);
            if (!emojiValidation.success) {
                return {
                    success: false,
                    error: `Pair ${i + 1}: ${emojiValidation.error}`
                };
            }

            // Validate label
            if (!label || label.length === 0) {
                return {
                    success: false,
                    error: `Empty label in pair ${i + 1}`
                };
            }

            if (label.length > 80) {
                return {
                    success: false,
                    error: `Label too long in pair ${i + 1}: "${label}". Maximum 80 characters`
                };
            }

            // Check for duplicate labels
            if (labels.has(label.toLowerCase())) {
                return {
                    success: false,
                    error: `Duplicate label: "${label}"`
                };
            }
            labels.add(label.toLowerCase());
        }

        return { success: true };
    }

    /**
     * Validate image URL
     * @param {string} url - URL to validate
     * @returns {Object} Validation result
     */
    validateImageUrl(url) {
        if (!url) {
            return { success: true }; // Optional parameter
        }

        try {
            const urlObj = new URL(url);
            
            if (!['http:', 'https:'].includes(urlObj.protocol)) {
                return {
                    success: false,
                    error: 'Image URL must use HTTP or HTTPS protocol'
                };
            }

            // Basic check for image file extensions
            const pathname = urlObj.pathname.toLowerCase();
            const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
            const hasImageExtension = imageExtensions.some(ext => pathname.endsWith(ext));
            
            // Allow URLs without extensions (many image hosting services don't use them)
            // Just warn if no extension is found
            if (!hasImageExtension) {
                console.log(`Warning: Image URL "${url}" doesn't have a common image file extension`);
            }

            return { success: true };

        } catch (error) {
            return {
                success: false,
                error: `Invalid image URL: ${error.message}`
            };
        }
    }

    /**
     * Validate panel title
     * @param {string} title - Title to validate
     * @returns {Object} Validation result
     */
    validateTitle(title) {
        if (!title || typeof title !== 'string') {
            return {
                success: false,
                error: 'Title cannot be empty'
            };
        }

        if (title.length > 256) {
            return {
                success: false,
                error: 'Title cannot exceed 256 characters'
            };
        }

        return { success: true };
    }

    /**
     * Validate panel description
     * @param {string} description - Description to validate
     * @returns {Object} Validation result
     */
    validateDescription(description) {
        // Allow empty descriptions (they will trigger default description generation)
        if (description === null || description === undefined) {
            return { success: true };
        }

        if (typeof description !== 'string') {
            return {
                success: false,
                error: 'Description must be a string'
            };
        }

        if (description.length > 4096) {
            return {
                success: false,
                error: 'Description cannot exceed 4096 characters'
            };
        }

        return { success: true };
    }
}

module.exports = new ReactionRoleValidator();
